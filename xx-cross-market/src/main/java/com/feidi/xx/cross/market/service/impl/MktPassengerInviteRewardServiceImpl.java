package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.MktPassengerInviteReward;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardService;
import com.feidi.xx.cross.market.service.reward.CouponRewardStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.resource.api.RemoteSmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客奖励发放记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRewardServiceImpl implements IMktPassengerInviteRewardService {

    private final MktPassengerInviteRewardMapper baseMapper;
    private final MktCouponMapper mktCouponMapper;
    private final List<RewardGrantStrategy> rewardStrategies;
    private final Map<String, RewardGrantStrategy> strategyMap = new HashMap<>();

    @DubboReference
    private final RemoteSmsService remoteSmsService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (RewardGrantStrategy strategy : rewardStrategies) {
            strategyMap.put(strategy.getRewardType(), strategy);
            log.info("注册奖励发放策略：{} -> {}", strategy.getRewardType(), strategy.getClass().getSimpleName());
        }
    }

    /**
     * 查询乘客推乘客奖励发放记录
     *
     * @param id 主键
     * @return 乘客推乘客奖励发放记录
     */
    @Override
    public MktPassengerInviteRewardVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客奖励发放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客奖励发放记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRewardVo> queryPageList(MktPassengerInviteRewardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRewardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客奖励发放记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客奖励发放记录列表
     */
    @Override
    public List<MktPassengerInviteRewardVo> queryList(MktPassengerInviteRewardBo bo) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteReward> buildQueryWrapper(MktPassengerInviteRewardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteReward::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviteRecordId() != null, MktPassengerInviteReward::getInviteRecordId, bo.getInviteRecordId());
        lqw.eq(bo.getRewardConfigId() != null, MktPassengerInviteReward::getRewardConfigId, bo.getRewardConfigId());
        lqw.eq(bo.getPassengerId() != null, MktPassengerInviteReward::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getRoleType() != null, MktPassengerInviteReward::getRoleType, bo.getRoleType());
        lqw.eq(bo.getConditionType() != null, MktPassengerInviteReward::getConditionType, bo.getConditionType());
        lqw.eq(bo.getRewardType() != null, MktPassengerInviteReward::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardValue() != null, MktPassengerInviteReward::getRewardValue, bo.getRewardValue());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteReward::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward add = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward update = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteReward entity) {
    }

    /**
     * 校验并批量删除乘客推乘客奖励发放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 创建并发放奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigVo 奖励配置
     * @param passengerId    奖励接收人ID
     * @param roleType       角色类型
     * @param conditionType  发放条件
     * @return 是否成功
     */
    @Override
    public Boolean createAndGrantReward(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfigVo,
                                        Long passengerId, String roleType, String conditionType) {
        try {
            log.info("开始创建并发放奖励，邀请记录ID：{}，乘客ID：{}，奖励类型：{}",
                    inviteRecordId, passengerId, rewardConfigVo.getRewardType());

            // 1. 先创建奖励记录，获取主键ID
            MktPassengerInviteRewardBo rewardBo = createRewardRecord(inviteRecordId, rewardConfigVo, passengerId, roleType, conditionType);

            // 2. 获取对应的奖励发放策略
            RewardGrantStrategy strategy = strategyMap.get(rewardConfigVo.getRewardType());
            if (strategy == null) {
                log.error("未找到对应的奖励发放策略，奖励类型：{}", rewardConfigVo.getRewardType());
                updateRewardStatus(rewardBo.getId(), false, "未找到对应的奖励发放策略");
                return false;
            }

            // 3. 执行奖励发放
            RewardGrantResult result = strategy.grantReward(passengerId, rewardConfigVo, rewardBo.getId());

            // 4. 如果是优惠券奖励且发放成功，发送短信通知
            if (result.isSuccess() && strategy instanceof CouponRewardStrategy &&
                CollUtil.isNotEmpty(rewardConfigVo.getRewardMeta().getCouponIds())) {
                CouponRewardStrategy couponStrategy = (CouponRewardStrategy) strategy;
                Long couponId = rewardConfigVo.getRewardMeta().getCouponIds().get(0);
                couponStrategy.sendSmsNotification(passengerId, couponId, conditionType);
            }

            // 5. 更新奖励记录状态
            boolean updateSuccess = updateRewardStatus(rewardBo.getId(), result.isSuccess(), result.getRemark());

            if (result.isSuccess() && updateSuccess) {
                log.info("创建并发放奖励成功，奖励记录ID：{}", rewardBo.getId());
                return true;
            } else {
                log.error("创建并发放奖励失败，奖励记录ID：{}，发放结果：{}，更新结果：{}",
                         rewardBo.getId(), result.isSuccess(), updateSuccess);
                return false;
            }

        } catch (Exception e) {
            log.error("创建并发放奖励异常，邀请记录ID：{}，乘客ID：{}", inviteRecordId, passengerId, e);
            throw new ServiceException("创建并发放奖励失败：" + e.getMessage());
        }
    }

    /**
     * 撤销奖励（客诉时使用）
     *
     * @param inviteRecordId 邀请记录ID
     * @return 是否成功
     */
    @Override
    public Boolean revokeRewards(Long inviteRecordId) {
        try {
            log.info("开始撤销奖励，邀请记录ID：{}", inviteRecordId);

            // 查询该邀请记录下的所有已发放奖励
            List<MktPassengerInviteRewardVo> rewards = queryByInviteRecordId(inviteRecordId);
            if (CollUtil.isEmpty(rewards)) {
                log.warn("查询到邀请记录下没有奖励，邀请记录ID：{}", inviteRecordId);
                return true;
            }

            boolean allSuccess = true;
            for (MktPassengerInviteRewardVo reward : rewards) {
                // 只撤销已发放的首单奖励
                if (PassengerInviteRewardStatusEnum.GRANTED.getCode().equals(reward.getStatus()) &&
                    PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(reward.getConditionType())) {

                    boolean revokeSuccess = revokeSingleReward(reward);
                    if (!revokeSuccess) {
                        allSuccess = false;
                    }
                }
            }

            log.info("撤销奖励完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
            return allSuccess;

        } catch (Exception e) {
            log.error("撤销奖励失败，邀请记录ID：{}", inviteRecordId, e);
            throw new ServiceException("撤销奖励失败：" + e.getMessage());
        }
    }

    /**
     * 根据邀请记录ID查询奖励列表
     *
     * @param inviteRecordId 邀请记录ID
     * @return 奖励列表
     */
    @Override
    public List<MktPassengerInviteRewardVo> queryByInviteRecordId(Long inviteRecordId) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = Wrappers.lambdaQuery();
        lqw.eq(MktPassengerInviteReward::getInviteRecordId, inviteRecordId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 创建奖励记录
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigVo 奖励配置
     * @param passengerId    奖励接收人ID
     * @param roleType       角色类型
     * @param conditionType  发放条件
     * @return 奖励记录BO
     */
    private MktPassengerInviteRewardBo createRewardRecord(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfigVo,
                                                          Long passengerId, String roleType, String conditionType) {
        MktPassengerInviteRewardBo rewardBo = new MktPassengerInviteRewardBo();
        rewardBo.setCampaignId(rewardConfigVo.getCampaignId());
        rewardBo.setInviteRecordId(inviteRecordId);
        rewardBo.setRewardConfigId(rewardConfigVo.getId());
        rewardBo.setPassengerId(passengerId);
        rewardBo.setRoleType(roleType);
        rewardBo.setConditionType(conditionType);
        rewardBo.setRewardType(rewardConfigVo.getRewardType());
        rewardBo.setRewardValue(rewardConfigVo.getRewardValue());
        rewardBo.setRewardMeta(rewardConfigVo.getRewardMeta());
        rewardBo.setStatus(PassengerInviteRewardStatusEnum.GRANT_FAILED.getCode()); // 初始状态为发放失败

        // 插入记录并获取主键
        if (insertByBo(rewardBo)) {
            return rewardBo;
        }
        throw new RuntimeException("创建奖励记录失败");
    }

    /**
     * 撤销单个奖励
     *
     * @param reward 奖励记录
     * @return 撤销是否成功
     */
    private boolean revokeSingleReward(MktPassengerInviteRewardVo reward) {
        try {
            log.info("开始撤销单个奖励，奖励ID：{}，类型：{}", reward.getId(), reward.getRewardType());

            // 获取对应的策略
            RewardGrantStrategy strategy = strategyMap.get(reward.getRewardType());
            if (strategy == null) {
                log.error("未找到对应的奖励撤销策略，奖励类型：{}", reward.getRewardType());
                updateRewardRevokeStatus(reward.getId(), false, "未找到对应的奖励撤销策略");
                return false;
            }

            // 执行奖励撤销
            var result = strategy.revokeReward(reward);

            // 更新奖励状态
            boolean updateSuccess = updateRewardRevokeStatus(reward.getId(), result.isSuccess(), result.getRemark());

            if (result.isSuccess() && updateSuccess) {
                log.info("撤销单个奖励成功，奖励ID：{}", reward.getId());
                return true;
            } else {
                log.error("撤销单个奖励失败，奖励ID：{}，撤销结果：{}，更新结果：{}",
                         reward.getId(), result.isSuccess(), updateSuccess);
                return false;
            }

        } catch (Exception e) {
            log.error("撤销单个奖励异常，奖励ID：{}", reward.getId(), e);
            return false;
        }
    }

    /**
     * 更新奖励记录状态
     *
     * @param rewardId     奖励记录ID
     * @param grantSuccess 发放是否成功
     * @param remark       备注信息
     * @return 是否更新成功
     */
    private boolean updateRewardStatus(Long rewardId, boolean grantSuccess, String remark) {
        var updateWrapper = Wrappers.<MktPassengerInviteReward>lambdaUpdate()
                .set(MktPassengerInviteReward::getStatus,
                        grantSuccess ? PassengerInviteRewardStatusEnum.GRANTED.getCode()
                                : PassengerInviteRewardStatusEnum.GRANT_FAILED.getCode())
                .set(MktPassengerInviteReward::getRemark, remark)
                .eq(MktPassengerInviteReward::getId, rewardId);
        return baseMapper.update(updateWrapper) > 0;
    }

    /**
     * 更新奖励撤销状态
     *
     * @param rewardId     奖励记录ID
     * @param revokeSuccess 撤销是否成功
     * @param remark       备注信息
     * @return 是否更新成功
     */
    private boolean updateRewardRevokeStatus(Long rewardId, boolean revokeSuccess, String remark) {
        var updateWrapper = Wrappers.<MktPassengerInviteReward>lambdaUpdate()
                .set(MktPassengerInviteReward::getStatus,
                        revokeSuccess ? PassengerInviteRewardStatusEnum.REFUND.getCode()
                                : PassengerInviteRewardStatusEnum.REFUND_FAILED.getCode())
                .set(MktPassengerInviteReward::getRefundRewardTime, new Date())
                .set(MktPassengerInviteReward::getRemark, remark != null ? remark : "客诉撤销奖励")
                .eq(MktPassengerInviteReward::getId, rewardId);
        return baseMapper.update(updateWrapper) > 0;
    }



    /**
     * 发送短信通知
     *
     * @param passengerId 乘客ID
     * @param couponId 优惠券ID
     * @param conditionType 发放条件类型
     */
    public void sendSms(Long passengerId, Long couponId, String conditionType) {
        try {
            log.info("开始发送优惠券奖励短信通知，乘客ID：{}，优惠券ID：{}，条件类型：{}",
                    passengerId, couponId, conditionType);

            // 获取条件类型枚举
            PassengerInviteConditionTypeEnum conditionTypeEnum = PassengerInviteConditionTypeEnum.getByCodeThrow(conditionType);

            // 获取乘客信息
            RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(passengerId);
            if (passengerInfo == null) {
                log.warn("获取乘客信息失败，无法发送短信，乘客ID：{}", passengerId);
                return;
            }

            // 获取优惠券信息
            MktCoupon mktCoupon = mktCouponMapper.selectById(couponId);
            if (mktCoupon == null) {
                log.warn("获取优惠券信息失败，无法发送短信，优惠券ID：{}", couponId);
                return;
            }

            // 根据条件类型确定短信内容
            String smsContent = null;
//            if (PassengerInviteConditionTypeEnum.REGISTER.equals(conditionTypeEnum)) {
//                // 邀请人获得好友注册奖励时发送短信
//                smsContent = String.format("【喜行出行】您成功邀请好友注册，%s元优惠券已到账！快到喜行出行小程序使用吧～",
//                        mktCoupon.getAmount());
//            } else if (PassengerInviteConditionTypeEnum.FIRST_ORDER.equals(conditionTypeEnum)) {
//                // 邀请人获得好友完单奖励时发送短信
//                smsContent = String.format("【喜行出行】您的好友已完成首单，%s元优惠券已到账！快到喜行出行小程序使用吧～",
//                        mktCoupon.getAmount());
//            } else {
//                log.warn("未知的条件类型，无法发送短信，条件类型：{}", conditionType);
//                return;
//            }

            // TODO: 调用短信服务发送短信
            // remoteSmsService.sendSms(passengerInfo.getPhone(), smsContent);

            log.info("优惠券奖励短信通知发送成功，乘客ID：{}，手机号：{}，内容：{}",
                    passengerId, passengerInfo.getPhone(), smsContent);

        } catch (Exception e) {
            log.error("发送优惠券奖励短信通知失败，乘客ID：{}，优惠券ID：{}", passengerId, couponId, e);
        }
    }
}
