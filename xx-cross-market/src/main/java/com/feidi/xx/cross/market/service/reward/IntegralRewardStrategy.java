package com.feidi.xx.cross.market.service.reward;

import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.result.RewardRevokeResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 积分奖励策略实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IntegralRewardStrategy implements RewardGrantStrategy {
    
    @Override
    public RewardGrantResult grantReward(Long passengerId, MktPassengerInviteRewardConfigVo rewardConfigVo, Long rewardRecordId) {
        try {
            log.info("开始发放积分奖励，乘客ID：{}，积分：{}", passengerId, rewardConfigVo.getRewardValue());
            
            // TODO: 调用积分服务发放积分奖励
            // 这里暂时返回成功，实际需要调用相应的服务
            // 示例代码：
            // IntegralGrantRequest request = new IntegralGrantRequest();
            // request.setUserId(passengerId);
            // request.setPoints(rewardConfigVo.getRewardValue().intValue());
            // request.setSourceType("INVITE_REWARD");
            // request.setSourceId(rewardRecordId);
            // IntegralGrantResponse response = integralService.grantPoints(request);
            // if (response.isSuccess()) {
            //     return RewardGrantResult.success("积分奖励发放成功");
            // } else {
            //     return RewardGrantResult.fail(response.getErrorMessage());
            // }
            
            log.info("积分奖励发放成功，乘客ID：{}，积分：{}", passengerId, rewardConfigVo.getRewardValue());
            return RewardGrantResult.success("积分奖励发放成功");
            
        } catch (Exception e) {
            log.error("积分奖励发放失败，乘客ID：{}，积分：{}", passengerId, rewardConfigVo.getRewardValue(), e);
            return RewardGrantResult.fail("积分奖励发放失败：" + e.getMessage());
        }
    }
    
    @Override
    public RewardRevokeResult revokeReward(MktPassengerInviteRewardVo rewardVo) {
        try {
            log.info("开始撤销积分奖励，乘客ID：{}，积分：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue());
            
            // TODO: 调用积分服务撤销积分奖励
            // 这里暂时返回成功，实际需要调用相应的服务
            // 示例代码：
            // IntegralRevokeRequest request = new IntegralRevokeRequest();
            // request.setUserId(rewardVo.getPassengerId());
            // request.setPoints(rewardVo.getRewardValue().intValue());
            // request.setSourceType("INVITE_REWARD");
            // request.setSourceId(rewardVo.getId());
            // IntegralRevokeResponse response = integralService.revokePoints(request);
            // if (response.isSuccess()) {
            //     return RewardRevokeResult.success("积分奖励撤销成功");
            // } else {
            //     return RewardRevokeResult.fail(response.getErrorMessage());
            // }
            
            log.info("积分奖励撤销成功，乘客ID：{}，积分：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue());
            return RewardRevokeResult.success("积分奖励撤销成功");
            
        } catch (Exception e) {
            log.error("积分奖励撤销失败，乘客ID：{}，积分：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue(), e);
            return RewardRevokeResult.fail("积分奖励撤销失败：" + e.getMessage());
        }
    }
    
    @Override
    public String getRewardType() {
        return PassengerInviteRewardTypeEnum.INTEGRAL.getCode();
    }
}
