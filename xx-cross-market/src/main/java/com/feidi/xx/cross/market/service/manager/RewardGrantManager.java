package com.feidi.xx.cross.market.service.manager;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.service.reward.CouponRewardStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 奖励发放管理器
 * 使用策略模式管理不同类型的奖励发放
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RewardGrantManager {

    private final List<RewardGrantStrategy> rewardStrategies;
    private final Map<String, RewardGrantStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (RewardGrantStrategy strategy : rewardStrategies) {
            strategyMap.put(strategy.getRewardType(), strategy);
            log.info("注册奖励发放策略：{} -> {}", strategy.getRewardType(), strategy.getClass().getSimpleName());
        }
    }

    /**
     * 发放注册奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs 奖励配置列表
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantRegistrationRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                          Long inviterId, Long inviteeId) {
        log.info("开始发放注册奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放注册奖励");
            return false;
        }

        boolean allSuccess = true;

        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理注册条件的奖励
            if (!PassengerInviteConditionTypeEnum.REGISTER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }

            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }

            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("注册奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}",
                         inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }

        log.info("注册奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }

    /**
     * 发放首单奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs 奖励配置列表
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantFirstOrderRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                        Long inviterId, Long inviteeId) {
        log.info("开始发放首单奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放首单奖励");
            return false;
        }

        boolean allSuccess = true;

        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理首单条件的奖励
            if (!PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }

            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }

            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("首单奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}",
                         inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }

        log.info("首单奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }

    /**
     * 发放单个奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfig 奖励配置
     * @param receiverId 接收人ID
     * @return 发放是否成功
     */
    private boolean grantSingleReward(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfig, Long receiverId) {
        try {
            // 获取对应的策略
            RewardGrantStrategy strategy = strategyMap.get(rewardConfig.getRewardType());
            if (strategy == null) {
                log.error("未找到对应的奖励发放策略，奖励类型：{}", rewardConfig.getRewardType());
                return false;
            }

            // 这里需要先创建奖励记录，获取记录ID
            // 暂时使用inviteRecordId作为rewardRecordId，实际应该先创建奖励记录
            Long rewardRecordId = inviteRecordId; // TODO: 应该先创建奖励记录

            // 执行奖励发放
            RewardGrantResult result = strategy.grantReward(receiverId, rewardConfig, rewardRecordId);

            if (result.isSuccess()) {
                log.info("奖励发放成功，接收人ID：{}，奖励类型：{}，备注：{}",
                        receiverId, rewardConfig.getRewardType(), result.getRemark());

                // 如果是优惠券奖励，发送短信通知
                if (strategy instanceof CouponRewardStrategy couponRewardStrategy && CollUtil.isNotEmpty(rewardConfig.getRewardMeta().getCouponIds())) {
                    Long couponId = rewardConfig.getRewardMeta().getCouponIds().get(0);
                    couponRewardStrategy.sendSmsNotification(receiverId, couponId, rewardConfig.getConditionType());
                }

                return true;
            } else {
                log.error("奖励发放失败，接收人ID：{}，奖励类型：{}，错误：{}",
                         receiverId, rewardConfig.getRewardType(), result.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("发放奖励时发生异常，接收人ID：{}，奖励配置ID：{}", receiverId, rewardConfig.getId(), e);
            return false;
        }
    }

    /**
     * 根据角色类型确定奖励接收人
     *
     * @param roleType 角色类型
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 接收人ID
     */
    private Long determineReceiver(String roleType, Long inviterId, Long inviteeId) {
        if (PassengerInviteRoleTypeEnum.INVITER.getCode().equals(roleType)) {
            return inviterId;
        } else if (PassengerInviteRoleTypeEnum.INVITEE.getCode().equals(roleType)) {
            return inviteeId;
        } else {
            log.warn("未知的角色类型：{}", roleType);
            return null;
        }
    }
}
