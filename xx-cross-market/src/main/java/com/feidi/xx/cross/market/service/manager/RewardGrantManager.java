package com.feidi.xx.cross.market.service.manager;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.service.reward.CouponRewardStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 奖励发放管理器
 * 使用策略模式管理不同类型的奖励发放
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RewardGrantManager {
    
    private final List<RewardGrantStrategy> rewardStrategies;
    private final Map<String, RewardGrantStrategy> strategyMap = new HashMap<>();
    
    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (RewardGrantStrategy strategy : rewardStrategies) {
            strategyMap.put(strategy.getRewardType(), strategy);
            log.info("注册奖励发放策略：{} -> {}", strategy.getRewardType(), strategy.getClass().getSimpleName());
        }
    }
    
    /**
     * 发放注册奖励
     * 
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs 奖励配置列表
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantRegistrationRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs, 
                                          Long inviterId, Long inviteeId) {
        log.info("开始发放注册奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);
        
        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放注册奖励");
            return false;
        }
        
        boolean allSuccess = true;
        
        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理注册条件的奖励
            if (!PassengerInviteConditionTypeEnum.REGISTER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }
            
            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }
            
            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("注册奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}", 
                         inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }
        
        log.info("注册奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }
    
    /**
     * 发放首单奖励
     * 
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs 奖励配置列表
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantFirstOrderRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs, 
                                        Long inviterId, Long inviteeId) {
        log.info("开始发放首单奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);
        
        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放首单奖励");
            return false;
        }
        
        boolean allSuccess = true;
        
        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理首单条件的奖励
            if (!PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }
            
            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }
            
            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("首单奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}", 
                         inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }
        
        log.info("首单奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }
    
    /**
     * 根据角色类型确定奖励接收人
     * 
     * @param roleType 角色类型
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 接收人ID
     */
    private Long determineReceiver(String roleType, Long inviterId, Long inviteeId) {
        if (PassengerInviteRoleTypeEnum.INVITER.getCode().equals(roleType)) {
            return inviterId;
        } else if (PassengerInviteRoleTypeEnum.INVITEE.getCode().equals(roleType)) {
            return inviteeId;
        } else {
            log.warn("未知的角色类型：{}", roleType);
            return null;
        }
    }
}
