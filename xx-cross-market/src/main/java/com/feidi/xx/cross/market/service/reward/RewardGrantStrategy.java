package com.feidi.xx.cross.market.service.reward;

import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.result.RewardRevokeResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;

/**
 * 奖励发放策略接口
 * 
 * <AUTHOR>
 */
public interface RewardGrantStrategy {
    
    /**
     * 发放奖励
     * 
     * @param passengerId 乘客ID
     * @param rewardConfigVo 奖励配置
     * @param rewardRecordId 奖励记录ID（用于优惠券发放时的sourceId）
     * @return 发放结果
     */
    RewardGrantResult grantReward(Long passengerId, MktPassengerInviteRewardConfigVo rewardConfigVo, Long rewardRecordId);
    
    /**
     * 撤销奖励
     * 
     * @param rewardVo 奖励记录
     * @return 撤销结果
     */
    RewardRevokeResult revokeReward(MktPassengerInviteRewardVo rewardVo);
    
    /**
     * 获取奖励类型
     * 
     * @return 奖励类型代码
     */
    String getRewardType();
}
