package com.feidi.xx.cross.market.domain.bo;

import cn.hutool.crypto.SecureUtil;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class GetPassengerInviteCampaignShareBo {
//
//    /**
//     * 应用id
//     */
//    @NotBlank(message = "应用id不能为空")
//    private String appId;
//    /**
//     * 页面路径，非必填
//     */
//    private String page;
//
//    /**
//     * 小程序版本，非必填
//     */
//    private String envVersion;
    /**
     * 城市code（ 通过城市code查询）
     */
    @NotBlank(message = "城市code不能为空")
    private String cityCode;
    /*
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long campaignId;

    //    public String getKey() {
//        return SecureUtil.md5(appId + page + envVersion + cityCode + campaignId + LoginHelper.getUserId());
//    }
    public String getKey() {
        return SecureUtil.md5(cityCode + campaignId + LoginHelper.getUserId());
    }
}
