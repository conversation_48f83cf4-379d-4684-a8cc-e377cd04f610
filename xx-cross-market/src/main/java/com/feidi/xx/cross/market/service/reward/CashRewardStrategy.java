package com.feidi.xx.cross.market.service.reward;

import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.result.RewardRevokeResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 现金奖励策略实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CashRewardStrategy implements RewardGrantStrategy {
    
    @Override
    public RewardGrantResult grantReward(Long passengerId, MktPassengerInviteRewardConfigVo rewardConfigVo, Long rewardRecordId) {
        try {
            log.info("开始发放现金奖励，乘客ID：{}，金额：{}", passengerId, rewardConfigVo.getRewardValue());
            
            // TODO: 调用钱包服务发放现金奖励
            // 这里暂时返回成功，实际需要调用相应的服务
            
            log.info("现金奖励发放成功，乘客ID：{}，金额：{}", passengerId, rewardConfigVo.getRewardValue());
            return RewardGrantResult.success("现金奖励发放成功");
            
        } catch (Exception e) {
            log.error("现金奖励发放失败，乘客ID：{}，金额：{}", passengerId, rewardConfigVo.getRewardValue(), e);
            return RewardGrantResult.fail("现金奖励发放失败：" + e.getMessage());
        }
    }
    
    @Override
    public RewardRevokeResult revokeReward(MktPassengerInviteRewardVo rewardVo) {
        try {
            log.info("开始撤销现金奖励，乘客ID：{}，金额：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue());
            
            // TODO: 调用钱包服务撤销现金奖励
            // 这里暂时返回成功，实际需要调用相应的服务
            
            log.info("现金奖励撤销成功，乘客ID：{}，金额：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue());
            return RewardRevokeResult.success("现金奖励撤销成功");
            
        } catch (Exception e) {
            log.error("现金奖励撤销失败，乘客ID：{}，金额：{}", rewardVo.getPassengerId(), rewardVo.getRewardValue(), e);
            return RewardRevokeResult.fail("现金奖励撤销失败：" + e.getMessage());
        }
    }
    
    @Override
    public String getRewardType() {
        return PassengerInviteRewardTypeEnum.CASH.getCode();
    }
}
