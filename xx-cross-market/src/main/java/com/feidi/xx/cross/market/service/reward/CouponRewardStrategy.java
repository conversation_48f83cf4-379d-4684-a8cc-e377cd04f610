package com.feidi.xx.cross.market.service.reward;

import cn.hutool.core.thread.ThreadUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.bo.CouponGrantToUserBo;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.result.RewardRevokeResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.resource.api.RemoteSmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 优惠券奖励策略实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CouponRewardStrategy implements RewardGrantStrategy {
    
    private final IMktCouponGrantService mktCouponGrantService;
    private final MktCouponGrantMapper mktCouponGrantMapper;
    private final MktCouponMapper mktCouponMapper;
    
    @DubboReference
    private final RemoteSmsService remoteSmsService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;
    
    @Override
    public RewardGrantResult grantReward(Long passengerId, MktPassengerInviteRewardConfigVo rewardConfigVo, Long rewardRecordId) {
        try {
            log.info("开始发放优惠券奖励，乘客ID：{}，优惠券配置：{}", passengerId, rewardConfigVo.getRewardMeta());
            
            // 获取优惠券ID
            Long couponId = rewardConfigVo.getRewardMeta().getCouponIds().get(0);
            
            // 发放优惠券
            CouponGrantToUserBo couponGrantToUserBo = mktCouponGrantService.grantCoupon(CouponGrantToUserBo.builder()
                    .couponId(couponId)
                    .userId(passengerId)
                    .grantCount(1)
                    .sourceId(rewardRecordId) // 使用奖励记录ID作为sourceId
                    .sourceType(UserCouponSourceEnum.PASSENGER_INVITE.getCode())
                    .build());
            
            boolean grantSuccess = couponGrantToUserBo.getResult().equals(com.feidi.xx.cross.common.enums.market.CouponGrantToUserResultEnum.SUCCESS);
            String remark = couponGrantToUserBo.getResult().getMessage();
            
            if (grantSuccess) {
                log.info("优惠券奖励发放成功，乘客ID：{}，优惠券ID：{}", passengerId, couponId);
                return RewardGrantResult.success(remark);
            } else {
                log.warn("优惠券奖励发放失败，乘客ID：{}，优惠券ID：{}，原因：{}", passengerId, couponId, remark);
                return RewardGrantResult.fail(remark);
            }
            
        } catch (Exception e) {
            log.error("优惠券奖励发放失败，乘客ID：{}，优惠券配置：{}", passengerId, rewardConfigVo.getRewardMeta(), e);
            return RewardGrantResult.fail("优惠券奖励发放失败：" + e.getMessage());
        }
    }
    
    @Override
    public RewardRevokeResult revokeReward(MktPassengerInviteRewardVo rewardVo) {
        try {
            log.info("开始撤销优惠券奖励，乘客ID：{}，奖励记录ID：{}", rewardVo.getPassengerId(), rewardVo.getId());
            
            // 获取优惠券ID
            Long couponId = rewardVo.getRewardMeta().getCouponIds().get(0);
            
            // 使用MktCouponGrantMapper的revoke方法批量撤回优惠券
            // 基于sourceType=PASSENGER_INVITE和sourceId=奖励记录ID进行撤回
            mktCouponGrantMapper.revoke(UserCouponSourceEnum.PASSENGER_INVITE, rewardVo.getId(), couponId);
            
            log.info("优惠券奖励撤销成功，乘客ID：{}，优惠券ID：{}", rewardVo.getPassengerId(), couponId);
            return RewardRevokeResult.success("优惠券奖励撤销成功");
            
        } catch (Exception e) {
            log.error("优惠券奖励撤销失败，乘客ID：{}，奖励记录ID：{}", rewardVo.getPassengerId(), rewardVo.getId(), e);
            return RewardRevokeResult.fail("优惠券奖励撤销失败：" + e.getMessage());
        }
    }
    
    @Override
    public String getRewardType() {
        return PassengerInviteRewardTypeEnum.COUPON.getCode();
    }
    
    /**
     * 发送短信通知
     * 
     * @param passengerId 乘客ID
     * @param couponId 优惠券ID
     * @param conditionType 发放条件类型
     */
    public void sendSmsNotification(Long passengerId, Long couponId, String conditionType) {
        ThreadUtil.execAsync(() -> {
            try {
                log.info("开始发送优惠券奖励短信通知，乘客ID：{}，优惠券ID：{}", passengerId, couponId);
                
                // 获取乘客信息
                RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(passengerId);
                if (passengerInfo == null) {
                    log.warn("获取乘客信息失败，无法发送短信，乘客ID：{}", passengerId);
                    return;
                }
                
                // 获取优惠券信息
                MktCoupon mktCoupon = mktCouponMapper.selectById(couponId);
                if (mktCoupon == null) {
                    log.warn("获取优惠券信息失败，无法发送短信，优惠券ID：{}", couponId);
                    return;
                }
                
                // TODO: 实现短信发送逻辑
                // 根据conditionType确定短信模板
                // 邀请人获得好友注册奖励时发送短信：
                // 【XXXXXX】您成功邀请好友注册，{$优惠券金额}元优惠券已到账！快到喜行出行小程序使用吧～
                // 邀请人获得好友完单奖励时发送短信：
                // 【XXXXXX】您的好友已完成首单，{$优惠券金额}元优惠券已到账！快到喜行出行小程序使用吧～
                
                log.info("优惠券奖励短信通知发送成功，乘客ID：{}，优惠券ID：{}", passengerId, couponId);
                
            } catch (Exception e) {
                log.error("发送优惠券奖励短信通知失败，乘客ID：{}，优惠券ID：{}", passengerId, couponId, e);
            }
        });
    }
}
