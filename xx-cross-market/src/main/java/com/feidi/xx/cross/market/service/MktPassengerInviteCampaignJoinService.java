package com.feidi.xx.cross.market.service;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCampaignMapper;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 乘客邀请活动 参与活动
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MktPassengerInviteCampaignJoinService {

    private final MktPassengerInviteCampaignMbrService campaignMbrService;
    private final IMktPassengerInviteCodeService inviteCodeService;
    private final IMktPassengerInviteRewardConfigService inviteRewardConfigService;
    private final IMktPassengerInviteRecordService inviteRecordService;
    private final IMktPassengerInviteRewardService inviteRewardService;
    private final MktPassengerInviteCampaignMapper inviteCampaignMapper;

    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 乘客注册成功，参与活动
     */
    @Transactional(rollbackFor = Exception.class)
    public void registrationJoin(PassengerRegisterEvent passengerRegisterEvent) {
        if (passengerRegisterEvent.getInviteCode() == null) {
            log.debug("乘客注册成功，参与活动，传递了空的邀请码 参数{}", passengerRegisterEvent);
            return;
        }
        //判断有没有参与过活动
        var isJoin = inviteRecordService.isJoin(passengerRegisterEvent.getPassengerId());
        if (isJoin) {
            log.warn("乘客注册成功，参与活动，但已参与过活动参数{}", passengerRegisterEvent);
            return;
        }
        var mktPassengerInviteCampaignVo = campaignMbrService.queryByInviteCode(passengerRegisterEvent.getInviteCode());
        log.debug("乘客注册成功，参与活动，活动信息：{}", mktPassengerInviteCampaignVo);
        if (mktPassengerInviteCampaignVo == null) {
            log.warn("乘客注册成功，参与活动，传递了邀请码 但活动不存在 参数{}", passengerRegisterEvent);
            return;
        }
        // 检查活动状态
        if (!Objects.equals(mktPassengerInviteCampaignVo.getStatus(), PassengerInviteCampaignStatusEnum.ONGOING.getCode())) {
            log.warn("乘客注册成功，参与活动，活动不在进行中 参数{}", passengerRegisterEvent);
            return;
        }

        // 查询奖励配置
        var rewardConfigVos = inviteRewardConfigService.queryListByCampaignId(mktPassengerInviteCampaignVo.getId());
        if (CollUtil.isEmpty(rewardConfigVos)) {
            log.warn("乘客注册成功，参与活动，活动不存在奖励配置 参数{}", passengerRegisterEvent);
            return;
        }

        // 通过邀请码查找邀请人信息
        var inviteCodeEntity = inviteCodeService.queryByCode(passengerRegisterEvent.getInviteCode());
        if (inviteCodeEntity == null) {
            log.warn("乘客注册成功，参与活动，邀请码不存在 参数{}", passengerRegisterEvent);
            return;
        }

        // 创建邀请记录
        MktPassengerInviteRecordBo recordBo = new MktPassengerInviteRecordBo();
        recordBo.setCampaignId(mktPassengerInviteCampaignVo.getId());
        recordBo.setInviterId(inviteCodeEntity.getInviterId());
        recordBo.setInviterCityCode(inviteCodeEntity.getCityCode());
        recordBo.setInviteeId(passengerRegisterEvent.getPassengerId());
        recordBo.setInviteeMobile(passengerRegisterEvent.getPhone());
        recordBo.setInviteeCityCode(passengerRegisterEvent.getCityCode());
        recordBo.setInviteTime(new Date());
        recordBo.setStatus(PassengerInviteStatusEnum.REGISTERED.getCode());

        // 通过远程服务查询邀请人的手机号信息
        var inviter = remotePassengerService.getPassengerInfo(passengerRegisterEvent.getPassengerId());
        if (inviter == null) {
            log.error("乘客注册成功，参与活动，查询邀请人信息失败 参数{}", passengerRegisterEvent);
            return;
        }
        recordBo.setInviterMobile(inviter.getPhone());
        log.debug("registrationJoin, recordBo: {}", recordBo);

        Boolean recordCreated = inviteRecordService.insertByBo(recordBo);
        if (!recordCreated) {
            log.error("乘客注册成功，参与活动，创建邀请记录失败 参数{}", passengerRegisterEvent);
            return;
        }

        // 发放注册奖励
        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigVos) {
            if (PassengerInviteConditionTypeEnum.REGISTER.getCode().equals(rewardConfig.getConditionType())) {
                // 给邀请人发放奖励
                if (PassengerInviteRoleTypeEnum.INVITER.getCode().equals(rewardConfig.getRoleType()) && recordBo.getInviterId() != null) {
                    inviteRewardService.createAndGrantReward(recordBo.getId(), rewardConfig,
                            recordBo.getInviterId(), rewardConfig.getRoleType(), rewardConfig.getConditionType());
                }
                // 给被邀请人发放奖励
                if (PassengerInviteRoleTypeEnum.INVITEE.getCode().equals(rewardConfig.getRoleType())) {
                    inviteRewardService.createAndGrantReward(recordBo.getId(), rewardConfig,
                            recordBo.getInviteeId(), rewardConfig.getRoleType(), rewardConfig.getConditionType());
                }
            }
        }

        log.info("乘客注册成功，参与活动完成，创建邀请记录ID：{}", recordBo.getId());
    }

    /**
     * 乘客完单，满足首单，发放邀请活动的奖励
     *
     * @param statusChangeEvent 订单状态变更事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(OrdOrderStatusChangeEvent statusChangeEvent) {
        RemoteOrderVo remoteOrderVo = remoteOrderService.queryFirstOrderByPassengerId(statusChangeEvent.getPassengerId());
        if (remoteOrderVo == null) {
            log.error("查询乘客首单，订单不存在，event：{}", statusChangeEvent);
            return;
        }
        if (!remoteOrderVo.getOrderNo().equals(statusChangeEvent.getOrderNo())) {
            log.debug("不是首单 不用处理邀请活动相关的，event：{}，remoteOrderVo：{}", statusChangeEvent, remoteOrderVo);
            return;
        }
        // 查询邀请记录
        var inviteRecord = inviteRecordService.queryByInviteeId(statusChangeEvent.getPassengerId());
        if (inviteRecord == null) {
            log.debug("乘客完成首单，但没有邀请记录，event：{}", statusChangeEvent);
            return;
        }
        var mktPassengerInviteCampaignVo = inviteCampaignMapper.selectVoById(inviteRecord.getCampaignId());
        // 检查活动状态
        if (!Objects.equals(mktPassengerInviteCampaignVo.getStatus(), PassengerInviteCampaignStatusEnum.ONGOING.getCode())) {
            log.warn("乘客注册成功，参与活动，活动不在进行中 参数{}", statusChangeEvent);
            return;
        }

        // 更新邀请记录状态为已首单
        Boolean updated = inviteRecordService.updateToFirstOrder(
                inviteRecord.getId(),
                statusChangeEvent.getOrderId(),
                statusChangeEvent.getFinishTime()
        );
        if (!updated) {
            log.error("乘客完成首单，更新邀请记录状态失败，event：{}", statusChangeEvent);
            return;
        }

        // 查询奖励配置
        var rewardConfigVos = inviteRewardConfigService.queryListByCampaignId(inviteRecord.getCampaignId());
        if (CollUtil.isEmpty(rewardConfigVos)) {
            log.warn("[INVITE_REWARD_CFG_MISSING] 首单发奖配置缺失，campaignId={}，inviteRecordId={}，event={}",
                    inviteRecord.getCampaignId(), inviteRecord.getId(), statusChangeEvent);
            return;
        }

        // 发放首单奖励
        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigVos) {
            if (PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(rewardConfig.getConditionType())) {
                // 给邀请人发放奖励
                if (PassengerInviteRoleTypeEnum.INVITER.getCode().equals(rewardConfig.getRoleType()) && inviteRecord.getInviterId() != null) {
                    inviteRewardService.createAndGrantReward(inviteRecord.getId(), rewardConfig,
                            inviteRecord.getInviterId(), rewardConfig.getRoleType(), rewardConfig.getConditionType());
                }
                // 给被邀请人发放奖励
                if (PassengerInviteRoleTypeEnum.INVITEE.getCode().equals(rewardConfig.getRoleType())) {
                    inviteRewardService.createAndGrantReward(inviteRecord.getId(), rewardConfig,
                            inviteRecord.getInviteeId(), rewardConfig.getRoleType(), rewardConfig.getConditionType());
                }
            }
        }

        log.info("乘客完成首单，邀请活动奖励发放完成，邀请记录ID：{}，订单ID：{}", inviteRecord.getId(), statusChangeEvent.getOrderId());
    }

    /**
     * 乘客客诉取消订单，取消邀请活动的奖励
     *
     * @param statusChangeEvent 订单状态变更事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(OrdOrderStatusChangeEvent statusChangeEvent) {
        // 查询邀请记录
        var inviteRecord = inviteRecordService.queryByInviteeId(statusChangeEvent.getPassengerId());
        if (inviteRecord == null) {
            log.debug("乘客客诉，但没有邀请记录，event：{}", statusChangeEvent);
            return;
        }

        // 检查是否是首单客诉
        if (inviteRecord.getOrderId() != null && inviteRecord.getOrderId().equals(statusChangeEvent.getOrderId())) {
            // 撤销已发放的奖励
            Boolean revoked = inviteRewardService.revokeRewards(inviteRecord.getId());
            if (revoked) {
                log.info("乘客客诉，撤销邀请活动奖励成功，邀请记录ID：{}，订单ID：{}", inviteRecord.getId(), statusChangeEvent.getOrderId());
            } else {
                log.error("乘客客诉，撤销邀请活动奖励失败，邀请记录ID：{}，订单ID：{}", inviteRecord.getId(), statusChangeEvent.getOrderId());
            }
        } else {
            log.debug("乘客客诉，但不是首单客诉，不处理邀请活动奖励，event：{}", statusChangeEvent);
        }
    }

}
