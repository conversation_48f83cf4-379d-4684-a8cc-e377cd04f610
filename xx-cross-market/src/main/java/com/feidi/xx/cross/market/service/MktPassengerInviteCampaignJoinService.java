package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import com.feidi.xx.cross.market.common.ValidationResult;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.service.manager.InviteRecordManager;
import com.feidi.xx.cross.market.service.validator.InviteCampaignValidator;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 乘客邀请活动 参与活动
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MktPassengerInviteCampaignJoinService {

    private final InviteCampaignValidator campaignValidator;
    private final InviteRecordManager inviteRecordManager;

    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 乘客注册成功，参与活动
     */
    @Transactional(rollbackFor = Exception.class)
    public void registrationJoin(PassengerRegisterEvent passengerRegisterEvent) {
        try {
            log.info("开始处理乘客注册参与活动，乘客ID：{}，邀请码：{}",
                    passengerRegisterEvent.getPassengerId(), passengerRegisterEvent.getInviteCode());

            // 1. 验证前置条件
            var validationResult = campaignValidator.validateRegistrationParticipation(passengerRegisterEvent.getPassengerId(), passengerRegisterEvent.getInviteCode());

            if (validationResult.isFailed()) {
                log.warn("乘客注册参与活动验证失败：{}，参数：{}", validationResult.getErrorMessage(), passengerRegisterEvent);
                return;
            }

            // 2. 获取验证数据
            var data = validationResult.getData();

            // 3. 查询邀请人信息
            RemotePassengerVo inviter = remotePassengerService.getPassengerInfo(data.getInviteCode().getInviterId());
            if (inviter == null) {
                log.error("查询邀请人信息失败，邀请人ID：{}", data.getInviteCode().getInviterId());
                throw new ServiceException("查询邀请人信息失败");
            }

            // 4. 创建邀请记录
            MktPassengerInviteRecordBo recordBo = buildInviteRecord(passengerRegisterEvent, data, inviter);
            Long inviteRecordId = inviteRecordManager.createInviteRecord(recordBo);

            // 5. 发放注册奖励
            boolean rewardSuccess = inviteRecordManager.handleRegistrationRewards(
                    inviteRecordId,
                    data.getRewardConfigs(),
                    data.getInviteCode().getInviterId(),
                    passengerRegisterEvent.getPassengerId());

            if (rewardSuccess) {
                log.info("乘客注册参与活动完成，邀请记录ID：{}", inviteRecordId);
            } else {
                log.warn("乘客注册参与活动完成，但奖励发放部分失败，邀请记录ID：{}", inviteRecordId);
            }

        } catch (Exception e) {
            log.error("处理乘客注册参与活动失败，参数：{}", passengerRegisterEvent, e);
            throw new ServiceException("处理乘客注册参与活动失败：" + e.getMessage());
        }
    }

    /**
     * 乘客完单，满足首单，发放邀请活动的奖励
     *
     * @param statusChangeEvent 订单状态变更事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(OrdOrderStatusChangeEvent statusChangeEvent) {
        try {
            log.info("开始处理乘客首单完成，乘客ID：{}，订单号：{}",
                    statusChangeEvent.getPassengerId(), statusChangeEvent.getOrderNo());

            // 1. 验证是否为首单
            RemoteOrderVo remoteOrderVo = remoteOrderService.queryFirstOrderByPassengerId(statusChangeEvent.getPassengerId());
            if (remoteOrderVo == null) {
                log.error("查询乘客首单失败，订单不存在，event：{}", statusChangeEvent);
                return;
            }

            if (!remoteOrderVo.getOrderNo().equals(statusChangeEvent.getOrderNo())) {
                log.debug("不是首单，无需处理邀请活动，event：{}", statusChangeEvent);
                return;
            }

            // 2. 验证首单完成前置条件
            ValidationResult<InviteCampaignValidator.FirstOrderValidationData> validationResult =
                    campaignValidator.validateFirstOrderCompletion(
                            statusChangeEvent.getPassengerId(),
                            statusChangeEvent.getOrderNo());

            if (validationResult.isFailed()) {
                log.warn("首单完成验证失败：{}，event：{}", validationResult.getErrorMessage(), statusChangeEvent);
                return;
            }

            // 3. 获取验证数据
            InviteCampaignValidator.FirstOrderValidationData data = validationResult.getData();

            // 4. 更新邀请记录状态为已首单
            boolean updated = inviteRecordManager.updateToFirstOrderStatus(
                    data.getInviteRecord().getId(),
                    statusChangeEvent.getOrderId(),
                    statusChangeEvent.getFinishTime());

            if (!updated) {
                log.error("更新邀请记录状态失败，邀请记录ID：{}", data.getInviteRecord().getId());
                throw new ServiceException("更新邀请记录状态失败");
            }

            // 5. 发放首单奖励
            boolean rewardSuccess = inviteRecordManager.handleFirstOrderRewards(
                    data.getInviteRecord().getId(),
                    data.getRewardConfigs(),
                    data.getInviteRecord().getInviterId(),
                    data.getInviteRecord().getInviteeId());

            if (rewardSuccess) {
                log.info("乘客首单完成处理成功，邀请记录ID：{}，订单ID：{}",
                        data.getInviteRecord().getId(), statusChangeEvent.getOrderId());
            } else {
                log.warn("乘客首单完成处理完成，但奖励发放部分失败，邀请记录ID：{}",
                        data.getInviteRecord().getId());
            }

        } catch (Exception e) {
            log.error("处理乘客首单完成失败，event：{}", statusChangeEvent, e);
            throw new ServiceException("处理乘客首单完成失败：" + e.getMessage());
        }
    }

    /**
     * 乘客客诉取消订单，取消邀请活动的奖励
     *
     * @param statusChangeEvent 订单状态变更事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(OrdOrderStatusChangeEvent statusChangeEvent) {
        try {
            log.info("开始处理乘客客诉撤销奖励，乘客ID：{}，订单ID：{}",
                    statusChangeEvent.getPassengerId(), statusChangeEvent.getOrderId());

            // 查询邀请记录
            var inviteRecord = inviteRecordManager.queryByInviteeId(statusChangeEvent.getPassengerId());
            if (inviteRecord == null) {
                log.debug("乘客客诉，但没有邀请记录，event：{}", statusChangeEvent);
                return;
            }

            // 检查是否是首单客诉
            if (inviteRecord.getOrderId() != null && inviteRecord.getOrderId().equals(statusChangeEvent.getOrderId())) {
                // 撤销已发放的奖励
                boolean revoked = inviteRecordManager.revokeRewards(inviteRecord.getId());
                if (revoked) {
                    log.info("乘客客诉撤销邀请活动奖励成功，邀请记录ID：{}，订单ID：{}",
                            inviteRecord.getId(), statusChangeEvent.getOrderId());
                } else {
                    log.error("乘客客诉撤销邀请活动奖励失败，邀请记录ID：{}，订单ID：{}",
                            inviteRecord.getId(), statusChangeEvent.getOrderId());
                }
            } else {
                log.debug("乘客客诉，但不是首单客诉，不处理邀请活动奖励，event：{}", statusChangeEvent);
            }

        } catch (Exception e) {
            log.error("处理乘客客诉撤销奖励失败，event：{}", statusChangeEvent, e);
            throw new ServiceException("处理乘客客诉撤销奖励失败：" + e.getMessage());
        }
    }

    /**
     * 构建邀请记录
     *
     * @param event   乘客注册事件
     * @param data    验证数据
     * @param inviter 邀请人信息
     * @return 邀请记录BO
     */
    private MktPassengerInviteRecordBo buildInviteRecord(PassengerRegisterEvent event,
                                                         InviteCampaignValidator.RegistrationValidationData data,
                                                         RemotePassengerVo inviter) {
        MktPassengerInviteRecordBo recordBo = new MktPassengerInviteRecordBo();
        recordBo.setCampaignId(data.getCampaign().getId());
        recordBo.setInviterId(data.getInviteCode().getInviterId());
        recordBo.setInviterCityCode(data.getInviteCode().getCityCode());
        recordBo.setInviterMobile(inviter.getPhone());
        recordBo.setInviteeId(event.getPassengerId());
        recordBo.setInviteeMobile(event.getPhone());
        recordBo.setInviteeCityCode(event.getCityCode());

        return recordBo;
    }

}
