package com.feidi.xx.cross.market.service.impl;

import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.bo.CouponGrantToUserBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.resource.api.RemoteSmsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MktPassengerInviteRewardServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class MktPassengerInviteRewardServiceImplTest {

    @Mock
    private MktPassengerInviteRewardMapper baseMapper;

    @Mock
    private IMktCouponGrantService mktCouponGrantService;

    @Mock
    private MktCouponGrantMapper mktCouponGrantMapper;

    @Mock
    private MktCouponMapper mktCouponMapper;

    @Mock
    private RemoteSmsService remoteSmsService;

    @Mock
    private RemotePassengerService remotePassengerService;

    @InjectMocks
    private MktPassengerInviteRewardServiceImpl service;

    private MktPassengerInviteRewardConfigVo rewardConfigVo;
    private Long inviteRecordId = 1001L;
    private Long passengerId = 2001L;
    private String roleType = "1";
    private String conditionType = "1";

    @BeforeEach
    void setUp() {
        // 设置优惠券奖励配置
        rewardConfigVo = new MktPassengerInviteRewardConfigVo();
        rewardConfigVo.setId(3001L);
        rewardConfigVo.setCampaignId(4001L);
        rewardConfigVo.setRewardType(PassengerInviteRewardTypeEnum.COUPON.getCode());
        rewardConfigVo.setRewardValue(new BigDecimal("10.00"));
        
        RewardMeta rewardMeta = new RewardMeta();
        rewardMeta.setCouponIds(Arrays.asList(5001L));
        rewardConfigVo.setRewardMeta(rewardMeta);
    }

    @Test
    void testCreateAndGrantReward_CouponSuccess() {
        // 模拟插入奖励记录成功
        when(baseMapper.insert(any())).thenAnswer(invocation -> {
            Object arg = invocation.getArgument(0);
            // 模拟设置ID
            if (arg instanceof com.feidi.xx.cross.market.domain.MktPassengerInviteReward) {
                ((com.feidi.xx.cross.market.domain.MktPassengerInviteReward) arg).setId(6001L);
            }
            return 1;
        });

        // 模拟优惠券发放成功
        CouponGrantToUserBo successResult = CouponGrantToUserBo.builder()
                .result(CouponGrantToUserResultEnum.SUCCESS)
                .couponGrantId(7001L)
                .build();
        when(mktCouponGrantService.grantCoupon(any(CouponGrantToUserBo.class))).thenReturn(successResult);

        // 模拟更新奖励记录成功
        when(baseMapper.updateById(any())).thenReturn(1);

        // 执行测试
        Boolean result = service.createAndGrantReward(inviteRecordId, rewardConfigVo, passengerId, roleType, conditionType);

        // 验证结果
        assertTrue(result);

        // 验证优惠券发放时使用了正确的sourceId
        verify(mktCouponGrantService).grantCoupon(argThat(bo -> 
            bo.getSourceId().equals(6001L) && 
            bo.getSourceType().equals(UserCouponSourceEnum.PASSENGER_INVITE.getCode()) &&
            bo.getCouponId().equals(5001L) &&
            bo.getUserId().equals(passengerId)
        ));
    }

    @Test
    void testCreateAndGrantReward_CouponFailed() {
        // 模拟插入奖励记录成功
        when(baseMapper.insert(any())).thenAnswer(invocation -> {
            Object arg = invocation.getArgument(0);
            if (arg instanceof com.feidi.xx.cross.market.domain.MktPassengerInviteReward) {
                ((com.feidi.xx.cross.market.domain.MktPassengerInviteReward) arg).setId(6002L);
            }
            return 1;
        });

        // 模拟优惠券发放失败
        CouponGrantToUserBo failResult = CouponGrantToUserBo.builder()
                .result(CouponGrantToUserResultEnum.NOT_ENOUGH)
                .build();
        when(mktCouponGrantService.grantCoupon(any(CouponGrantToUserBo.class))).thenReturn(failResult);

        // 模拟更新奖励记录成功
        when(baseMapper.updateById(any())).thenReturn(1);

        // 执行测试
        Boolean result = service.createAndGrantReward(inviteRecordId, rewardConfigVo, passengerId, roleType, conditionType);

        // 验证结果
        assertTrue(result); // 方法本身执行成功，但奖励状态会被设置为失败

        // 验证更新状态时使用了失败状态
        verify(baseMapper).updateById(argThat(reward -> 
            ((com.feidi.xx.cross.market.domain.MktPassengerInviteReward) reward).getStatus()
                .equals(PassengerInviteRewardStatusEnum.GRANT_FAILED.getCode())
        ));
    }

    @Test
    void testRevokeRewards_CouponSuccess() {
        // 准备测试数据
        MktPassengerInviteRewardVo reward = new MktPassengerInviteRewardVo();
        reward.setId(8001L);
        reward.setPassengerId(passengerId);
        reward.setRewardType(PassengerInviteRewardTypeEnum.COUPON.getCode());
        reward.setStatus(PassengerInviteRewardStatusEnum.GRANTED.getCode());
        reward.setConditionType(PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode());
        
        RewardMeta rewardMeta = new RewardMeta();
        rewardMeta.setCouponIds(Arrays.asList(5001L));
        reward.setRewardMeta(rewardMeta);

        // 模拟查询奖励记录
        when(baseMapper.selectVoList(any())).thenReturn(Arrays.asList(reward));

        // 模拟更新奖励记录成功
        when(baseMapper.update(any())).thenReturn(1);

        // 执行测试
        Boolean result = service.revokeRewards(inviteRecordId);

        // 验证结果
        assertTrue(result);

        // 验证调用了优惠券撤回方法
        verify(mktCouponGrantMapper).revoke(
            eq(UserCouponSourceEnum.PASSENGER_INVITE),
            eq(8001L),
            eq(5001L)
        );

        // 验证更新了奖励记录状态
        verify(baseMapper).update(any());
    }

    @Test
    void testRevokeRewards_NoRewards() {
        // 模拟没有查询到奖励记录
        when(baseMapper.selectVoList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        Boolean result = service.revokeRewards(inviteRecordId);

        // 验证结果
        assertTrue(result); // 没有奖励记录也应该返回成功

        // 验证没有调用撤回方法
        verify(mktCouponGrantMapper, never()).revoke(any(), any(), any());
    }
}
